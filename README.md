# 📱 iPhone Data Server

A Flask server that receives images, videos, and contacts from your iPhone 16 Pro Max via the Shortcuts app. Perfect for backing up or transferring data to your local computer with one tap!

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the Server

```bash
python app.py
```

The server will start at `http://localhost:5000` and be accessible from your local network.

### 3. Find Your Server IP

**Windows:**
```cmd
ipconfig
```

**Mac/Linux:**
```bash
ifconfig | grep inet
```

Look for your local IP address (usually starts with 192.168.x.x or 10.x.x.x).

### 4. Setup iPhone Shortcuts

Follow the detailed guide in `SHORTCUTS_SETUP_GUIDE.md` to create shortcuts on your iPhone.

## 📁 Project Structure

```
iphone/
├── app.py                    # Main Flask server
├── requirements.txt          # Python dependencies
├── shortcuts_config.json     # Shortcuts configuration reference
├── SHORTCUTS_SETUP_GUIDE.md  # Detailed setup instructions
├── README.md                # This file
└── uploads/                 # Created automatically
    ├── images/              # Uploaded photos
    ├── videos/              # Uploaded videos
    └── contacts/            # Exported contacts (JSON)
```

## 🔗 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Web dashboard with statistics |
| POST | `/upload/image` | Upload images from iPhone |
| POST | `/upload/video` | Upload videos from iPhone |
| POST | `/upload/contacts` | Upload contacts from iPhone |
| GET | `/status` | Server status and statistics |

## 📊 Web Dashboard

Visit `http://YOUR_SERVER_IP:5000` in your browser to see:
- Upload statistics (images, videos, contacts count)
- Recent uploaded files
- API endpoint documentation
- Setup instructions

## 🔧 Configuration

### Server Settings

Edit `app.py` to customize:
- **Port**: Change `port=5000` to your preferred port
- **Host**: Change `host='0.0.0.0'` to restrict access
- **Upload folders**: Modify `UPLOAD_FOLDERS` dictionary
- **File size limits**: Add file size validation

### iPhone Shortcuts

The shortcuts are configured to:
- Upload up to 10 photos at once
- Upload up to 5 videos at once
- Export all selected contacts
- Show notifications on completion

## 📱 Supported Data Types

### Images
- **Formats**: JPG, PNG, GIF, BMP
- **Encoding**: Base64
- **Storage**: `uploads/images/`
- **Naming**: `image_YYYYMMDD_HHMMSS_[uuid].jpg`

### Videos
- **Formats**: MP4, MOV, AVI, MKV
- **Encoding**: Base64
- **Storage**: `uploads/videos/`
- **Naming**: `video_YYYYMMDD_HHMMSS_[uuid].mp4`

### Contacts
- **Format**: JSON
- **Fields**: Name, Phone Numbers, Email Addresses, Organization
- **Storage**: `uploads/contacts/`
- **Naming**: `contacts_YYYYMMDD_HHMMSS.json`

## 🔐 Security Considerations

### Network Security
- Server runs on local network only
- No external internet access required
- CORS enabled for cross-origin requests

### Data Privacy
- All data stays on your local network
- No cloud services involved
- Files stored locally on your computer

### Recommendations
- Use on trusted WiFi networks only
- Consider adding authentication for production use
- Regularly backup uploaded files

## 🛠️ Troubleshooting

### Common Issues

**Server won't start:**
```bash
# Check if port is already in use
netstat -an | grep :5000

# Try a different port
python app.py  # Edit app.py to change port
```

**iPhone can't connect:**
- Verify both devices on same WiFi
- Check firewall settings
- Confirm server IP address
- Test server in browser: `http://YOUR_IP:5000`

**Large files failing:**
- Videos might be too large for base64 encoding
- Consider implementing file streaming for large videos
- Check available disk space

**Permissions denied:**
- Grant Shortcuts app access to Photos and Contacts
- Check iOS privacy settings

### Debug Mode

The server runs in debug mode by default. For production:

```python
app.run(host='0.0.0.0', port=5000, debug=False)
```

## 📈 Usage Statistics

The server tracks:
- Number of uploaded images
- Number of uploaded videos  
- Number of contact exports
- Recent file activity
- Upload timestamps

## 🔄 Updates and Maintenance

### Updating Server IP
If your computer's IP changes:
1. Find new IP address
2. Update shortcuts on iPhone
3. Update any bookmarks

### Backing Up Data
Regularly backup the `uploads/` folder to prevent data loss.

### Log Files
Server logs are displayed in the terminal. For persistent logging, modify `app.py` to write to log files.

## 🎯 Use Cases

- **Photo Backup**: Quickly backup photos from iPhone to computer
- **Video Transfer**: Transfer large videos without cloud services
- **Contact Export**: Export contacts for backup or migration
- **Bulk Operations**: Process multiple files at once
- **Local Development**: Test iOS app integrations locally

## 🤝 Contributing

Feel free to enhance this server with additional features:
- File compression
- Authentication
- Database storage
- Real-time sync
- Mobile app companion

## 📄 License

This project is open source. Use and modify as needed for your personal or commercial projects.

---

**Happy syncing! 📱➡️💻**
