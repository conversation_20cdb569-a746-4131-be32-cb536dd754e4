#!/usr/bin/env python3
"""
Test script for iPhone Data Server
Tests all endpoints to ensure they work correctly
"""

import requests
import json
import base64
import sys

SERVER_URL = "http://localhost:5000"

def create_test_image():
    """Create a small test image in base64 format"""
    # Create a minimal JPEG header for testing (1x1 pixel)
    # This is a valid minimal JPEG file
    jpeg_data = bytes([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
        0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
        0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
        0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
        0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x8A, 0x00,
        0xFF, 0xD9
    ])
    return base64.b64encode(jpeg_data).decode('utf-8')

def test_status_endpoint():
    """Test the status endpoint"""
    print("🔍 Testing status endpoint...")
    try:
        response = requests.get(f"{SERVER_URL}/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status endpoint working: {data['status']}")
            print(f"📊 Current stats: {data['stats']}")
            return True
        else:
            print(f"❌ Status endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Status endpoint error: {e}")
        return False

def test_image_upload():
    """Test image upload endpoint"""
    print("\n📸 Testing image upload...")
    try:
        test_image = create_test_image()
        payload = {"image": test_image}
        
        response = requests.post(
            f"{SERVER_URL}/upload/image",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Image upload successful: {data['filename']}")
            print(f"📏 File size: {data['size']} bytes")
            return True
        else:
            print(f"❌ Image upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Image upload error: {e}")
        return False

def test_contacts_upload():
    """Test contacts upload endpoint"""
    print("\n📞 Testing contacts upload...")
    try:
        test_contacts = [
            {
                "name": "Test Contact 1",
                "phone": "+1234567890",
                "email": "<EMAIL>"
            },
            {
                "name": "Test Contact 2", 
                "phone": "+0987654321",
                "email": "<EMAIL>"
            }
        ]
        
        payload = {"contacts": test_contacts}
        
        response = requests.post(
            f"{SERVER_URL}/upload/contacts",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Contacts upload successful: {data['filename']}")
            print(f"📊 Contact count: {data['count']}")
            return True
        else:
            print(f"❌ Contacts upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Contacts upload error: {e}")
        return False

def test_web_interface():
    """Test the web interface"""
    print("\n🌐 Testing web interface...")
    try:
        response = requests.get(SERVER_URL)
        if response.status_code == 200:
            if "iPhone Data Server" in response.text:
                print("✅ Web interface working correctly")
                return True
            else:
                print("❌ Web interface content incorrect")
                return False
        else:
            print(f"❌ Web interface failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web interface error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Starting iPhone Data Server Tests\n")
    
    tests = [
        ("Status Endpoint", test_status_endpoint),
        ("Web Interface", test_web_interface),
        ("Image Upload", test_image_upload),
        ("Contacts Upload", test_contacts_upload)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "="*50)
    print("📋 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All tests passed! Server is ready for iPhone integration.")
        print(f"\n📱 Your server is running at: {SERVER_URL}")
        print("🔗 Server IP for iPhone shortcuts: http://**************:5000")
    else:
        print("⚠️  Some tests failed. Check the server configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
