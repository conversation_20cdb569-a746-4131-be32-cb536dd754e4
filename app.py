from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import json
import base64
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Create directories for storing data
UPLOAD_FOLDERS = {
    'images': 'uploads/images',
    'videos': 'uploads/videos',
    'contacts': 'uploads/contacts'
}

for folder in UPLOAD_FOLDERS.values():
    os.makedirs(folder, exist_ok=True)

# HTML template for the web interface
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>iPhone Data Server</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint { background-color: #e8f4fd; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #0066cc; }
        .url { font-family: monospace; background: #f0f0f0; padding: 2px 5px; border-radius: 3px; }
        .stats { display: flex; justify-content: space-around; text-align: center; }
        .stat-box { background: #f8f9fa; padding: 15px; border-radius: 5px; min-width: 100px; }
        .stat-number { font-size: 24px; font-weight: bold; color: #28a745; }
        .recent-files { max-height: 200px; overflow-y: auto; }
        .file-item { padding: 5px; border-bottom: 1px solid #eee; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 iPhone Data Server</h1>
        
        <div class="section">
            <h2>📊 Statistics</h2>
            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number">{{ stats.images }}</div>
                    <div>Images</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">{{ stats.videos }}</div>
                    <div>Videos</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">{{ stats.contacts }}</div>
                    <div>Contacts</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔗 API Endpoints</h2>
            <div class="endpoint">
                <span class="method">POST</span> <span class="url">/upload/image</span> - Upload images from iPhone
            </div>
            <div class="endpoint">
                <span class="method">POST</span> <span class="url">/upload/video</span> - Upload videos from iPhone
            </div>
            <div class="endpoint">
                <span class="method">POST</span> <span class="url">/upload/contacts</span> - Upload contacts from iPhone
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <span class="url">/status</span> - Server status and statistics
            </div>
        </div>

        <div class="section">
            <h2>📁 Recent Files</h2>
            <div class="recent-files">
                {% for file in recent_files %}
                <div class="file-item">
                    <strong>{{ file.type }}</strong>: {{ file.name }} 
                    <small>({{ file.timestamp }})</small>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="section">
            <h2>📲 Setup Instructions</h2>
            <ol>
                <li>Import the provided .shortcuts file into your iPhone Shortcuts app</li>
                <li>Make sure your iPhone and this server are on the same network</li>
                <li>Run the shortcut to send data to this server</li>
                <li>Check this page to see uploaded files</li>
            </ol>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    # Get statistics
    stats = {
        'images': len([f for f in os.listdir(UPLOAD_FOLDERS['images']) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]),
        'videos': len([f for f in os.listdir(UPLOAD_FOLDERS['videos']) if f.lower().endswith(('.mp4', '.mov', '.avi', '.mkv'))]),
        'contacts': len([f for f in os.listdir(UPLOAD_FOLDERS['contacts']) if f.endswith('.json')])
    }
    
    # Get recent files
    recent_files = []
    for folder_type, folder_path in UPLOAD_FOLDERS.items():
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                recent_files.append({
                    'type': folder_type,
                    'name': filename,
                    'timestamp': datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')
                })
    
    # Sort by timestamp (newest first)
    recent_files.sort(key=lambda x: x['timestamp'], reverse=True)
    recent_files = recent_files[:10]  # Show only last 10 files
    
    return render_template_string(HTML_TEMPLATE, stats=stats, recent_files=recent_files)

@app.route('/upload/image', methods=['POST'])
def upload_image():
    try:
        data = request.get_json()
        
        if 'image' not in data:
            return jsonify({'error': 'No image data provided'}), 400
        
        # Decode base64 image
        image_data = data['image']
        if image_data.startswith('data:image'):
            # Remove data URL prefix
            image_data = image_data.split(',')[1]
        
        image_bytes = base64.b64decode(image_data)
        
        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"image_{timestamp}_{str(uuid.uuid4())[:8]}.jpg"
        filepath = os.path.join(UPLOAD_FOLDERS['images'], filename)
        
        # Save image
        with open(filepath, 'wb') as f:
            f.write(image_bytes)
        
        return jsonify({
            'success': True,
            'message': 'Image uploaded successfully',
            'filename': filename,
            'size': len(image_bytes)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload/video', methods=['POST'])
def upload_video():
    try:
        data = request.get_json()
        
        if 'video' not in data:
            return jsonify({'error': 'No video data provided'}), 400
        
        # Decode base64 video
        video_data = data['video']
        if video_data.startswith('data:video'):
            # Remove data URL prefix
            video_data = video_data.split(',')[1]
        
        video_bytes = base64.b64decode(video_data)
        
        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        extension = data.get('extension', 'mp4')
        filename = f"video_{timestamp}_{str(uuid.uuid4())[:8]}.{extension}"
        filepath = os.path.join(UPLOAD_FOLDERS['videos'], filename)
        
        # Save video
        with open(filepath, 'wb') as f:
            f.write(video_bytes)
        
        return jsonify({
            'success': True,
            'message': 'Video uploaded successfully',
            'filename': filename,
            'size': len(video_bytes)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/upload/contacts', methods=['POST'])
def upload_contacts():
    try:
        data = request.get_json()
        
        if 'contacts' not in data:
            return jsonify({'error': 'No contacts data provided'}), 400
        
        contacts = data['contacts']
        
        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"contacts_{timestamp}.json"
        filepath = os.path.join(UPLOAD_FOLDERS['contacts'], filename)
        
        # Save contacts
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(contacts, f, indent=2, ensure_ascii=False)
        
        return jsonify({
            'success': True,
            'message': 'Contacts uploaded successfully',
            'filename': filename,
            'count': len(contacts) if isinstance(contacts, list) else 1
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/status', methods=['GET'])
def status():
    stats = {
        'images': len([f for f in os.listdir(UPLOAD_FOLDERS['images']) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]),
        'videos': len([f for f in os.listdir(UPLOAD_FOLDERS['videos']) if f.lower().endswith(('.mp4', '.mov', '.avi', '.mkv'))]),
        'contacts': len([f for f in os.listdir(UPLOAD_FOLDERS['contacts']) if f.endswith('.json')])
    }
    
    return jsonify({
        'status': 'running',
        'server': 'iPhone Data Server',
        'stats': stats,
        'endpoints': [
            '/upload/image',
            '/upload/video', 
            '/upload/contacts',
            '/status'
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting iPhone Data Server...")
    print("📱 Server will be accessible at: http://localhost:5000")
    print("🌐 Make sure your iPhone is on the same network!")
    app.run(host='0.0.0.0', port=5000, debug=True)
