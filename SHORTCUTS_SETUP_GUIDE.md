# 📱 iPhone Shortcuts Setup Guide

This guide will help you create shortcuts on your iPhone 16 Pro Max (iOS 18) to send images, videos, and contacts to your local Flask server with one tap.

## 🔧 Prerequisites

1. **Server Running**: Make sure your Flask server is running on your computer
2. **Same Network**: Your iPhone and computer must be on the same WiFi network
3. **Server IP**: Find your computer's local IP address (usually starts with 192.168.x.x or 10.x.x.x)

### Finding Your Server IP Address

**On Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" under your WiFi adapter.

**On Mac/Linux:**
```bash
ifconfig | grep inet
```

## 📲 Creating the Shortcuts

### Shortcut 1: Send Photos to Server

1. Open **Shortcuts** app on your iPhone
2. Tap **+** to create a new shortcut
3. Name it "Send Photos to Server"
4. Add these actions in order:

   **Action 1: Select Photos**
   - Search for "Select Photos"
   - Enable "Select Multiple"
   - Set limit to 10 photos

   **Action 2: Text**
   - Search for "Text"
   - Enter: `http://YOUR_SERVER_IP:5000/upload/image`
   - Replace `YOUR_SERVER_IP` with your actual server IP

   **Action 3: Repeat with Each**
   - Search for "Repeat with Each"
   - Set input to "Photos" from Select Photos action

   **Inside the Repeat Loop:**
   
   **Action 4: Encode Media**
   - Search for "Encode Media"
   - Set format to "Base64"

   **Action 5: Get Contents of URL**
   - Search for "Get Contents of URL"
   - Set URL to "Text" from Action 2
   - Set Method to "POST"
   - Add Headers:
     - Key: `Content-Type`
     - Value: `application/json`
   - Set Request Body to "JSON" and add:
     ```json
     {
       "image": "[Base64 Encoded Media]"
     }
     ```

   **Action 6: Show Notification**
   - Search for "Show Notification"
   - Title: "Photos Uploaded"
   - Body: "Successfully sent photos to server"

### Shortcut 2: Send Videos to Server

1. Create a new shortcut named "Send Videos to Server"
2. Follow similar steps as photos but:
   - In Select Photos, choose "Videos" only
   - Change URL to: `http://YOUR_SERVER_IP:5000/upload/video`
   - In JSON body use:
     ```json
     {
       "video": "[Base64 Encoded Media]",
       "extension": "mp4"
     }
     ```

### Shortcut 3: Send Contacts to Server

1. Create a new shortcut named "Send Contacts to Server"
2. Add these actions:

   **Action 1: Get Contacts**
   - Search for "Get Contacts"
   - Enable "Select Multiple"

   **Action 2: Get Details of Contacts**
   - Search for "Get Details of Contacts"
   - Select details: Name, Phone Numbers, Email Addresses, Organization

   **Action 3: Text**
   - Enter: `http://YOUR_SERVER_IP:5000/upload/contacts`

   **Action 4: Get Contents of URL**
   - Set URL to text from Action 3
   - Method: "POST"
   - Headers: `Content-Type: application/json`
   - Request Body (JSON):
     ```json
     {
       "contacts": "[Contact Details]"
     }
     ```

   **Action 5: Show Notification**
   - Title: "Contacts Uploaded"
   - Body: "Successfully sent contacts to server"

### Shortcut 4: iPhone Data Sync (Master Shortcut)

1. Create a new shortcut named "iPhone Data Sync"
2. Add these actions:

   **Action 1: Ask for Input**
   - Type: "Choose from Menu"
   - Prompt: "What would you like to sync?"
   - Options:
     - Photos Only
     - Videos Only
     - Contacts Only
     - Photos & Videos
     - Everything

   **Action 2: Choose from Menu**
   - Input: "Chosen Item" from previous action
   - For each case, add "Run Shortcut" actions:
     - Photos Only → Run "Send Photos to Server"
     - Videos Only → Run "Send Videos to Server"
     - Contacts Only → Run "Send Contacts to Server"
     - Photos & Videos → Run both photo and video shortcuts
     - Everything → Run all three shortcuts

## 🔐 Permissions Setup

When you first run the shortcuts, iOS will ask for permissions:

1. **Photos Access**: Allow "Full Access" for photo and video shortcuts
2. **Contacts Access**: Allow "Full Access" for contacts shortcut
3. **Network Access**: Allow shortcuts to access the internet

## 🚀 Usage

### One-Tap Sync
1. Run the "iPhone Data Sync" shortcut
2. Choose what you want to sync
3. Select the items when prompted
4. Wait for the "Upload Complete" notification

### Individual Shortcuts
- Run "Send Photos to Server" to upload selected photos
- Run "Send Videos to Server" to upload selected videos  
- Run "Send Contacts to Server" to upload selected contacts

## 🏠 Adding to Home Screen

1. Open the shortcut in Shortcuts app
2. Tap the settings icon (⚙️)
3. Tap "Add to Home Screen"
4. Customize the icon and name
5. Tap "Add"

## 🔍 Troubleshooting

### Common Issues:

**"Could not connect to server"**
- Check if Flask server is running
- Verify both devices are on same WiFi
- Confirm server IP address is correct

**"Permission denied"**
- Go to Settings > Privacy & Security > Photos/Contacts
- Enable access for Shortcuts app

**"Large files failing"**
- Videos might be too large for base64 encoding
- Try smaller videos or reduce quality

**"Shortcut not working"**
- Check each action is configured correctly
- Verify JSON format in request body
- Test server endpoints in browser first

## 📊 Monitoring Uploads

Visit `http://YOUR_SERVER_IP:5000` in your browser to:
- See upload statistics
- View recent files
- Check server status
- Monitor successful uploads

## 🔄 Updates

To update server IP address:
1. Edit each shortcut
2. Update the Text action with new IP
3. Save the shortcut

Your shortcuts are now ready for one-tap data syncing! 🎉
